"use client"

import { AppSidebar } from "@/components/general/SideBar"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"

export default function SidebarPreview() {
  return (
    <div className="min-h-screen">
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <SidebarTrigger />
              <h1 className="text-2xl font-semibold">Petals AI Dashboard</h1>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h2 className="text-lg font-medium mb-4">Welcome to Petals AI</h2>
              <p className="text-gray-600">
                This is a preview of the Petals AI sidebar. The sidebar includes navigation 
                for all the main features including Dashboard, Chat, Symptom Contextualizer, 
                Pain & Fatigue Companion, Medication Optimizer, Menstrual Journal, History, 
                and Profile & Settings.
              </p>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  )
}