import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

export default function Home() {
  return (
    <div className="w-full min-h-screen p-6 sm:p-10 space-y-6">
      <header className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl sm:text-3xl font-semibold">Hello, Adebayo</h1>
          <p className="text-sm text-muted-foreground">
            Every step towards wellness counts. You’ve got this
          </p>
        </div>
        <div className="text-xs text-muted-foreground">
          Monday, May 26 • 09:41 AM
        </div>
      </header>

      <section className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
        {[
          { title: "Recent Symptom Analysis", cta: "View Full Report" },
          { title: "Upcoming Reminders", cta: "Manage All Reminders" },
          { title: "Chronic Condition Snapshot", cta: "View Plan" },
          { title: "Key Vitals", cta: "Vitals History" },
        ].map((card) => (
          <Card key={card.title} className="shadow-sm">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">{card.title}</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <Button variant="link" className="px-0 text-xs">{card.cta}</Button>
            </CardContent>
          </Card>
        ))}
      </section>

      <section className="space-y-3">
        <h2 className="text-lg font-semibold">Quick Mood</h2>
        <Card>
          <CardContent className="p-4 space-y-3">
            <div className="flex gap-2">
              <Button variant="outline" size="icon" aria-label="happy">🙂</Button>
              <Button variant="outline" size="icon" aria-label="neutral">😐</Button>
              <Button variant="outline" size="icon" aria-label="sad">🙁</Button>
            </div>
            <Textarea placeholder="Enter symptoms..." className="min-h-24" />
            <div className="flex justify-end">
              <Button>Submit Log</Button>
            </div>
          </CardContent>
        </Card>
      </section>

      <section className="space-y-3">
        <h2 className="text-lg font-semibold">Quick Actions</h2>
        <div className="flex flex-wrap gap-2">
          {["Log New Symptom", "Track My Day", "Manage Medications", "Chat with Petals"].map((a) => (
            <Button key={a} variant="outline" size="sm">
              {a}
            </Button>
          ))}
        </div>
      </section>

      <section className="space-y-3">
        <h2 className="text-lg font-semibold">Recent Activity & Insights</h2>
        <div className="space-y-2">
          {[
            "Symptom analysis completed - May 25",
            "New supplement suggestion: Magnesium Glycinate",
            "You logged ‘Anxious’ yesterday",
            "Personalized fatigue plan updated",
          ].map((t, i) => (
            <Card key={i}>
              <CardContent className="p-3 text-sm flex items-center justify-between gap-4">
                <span>{t}</span>
                <Button variant="link" className="px-0 text-xs">View</Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      <section className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="min-h-64">
            <CardHeader className="pb-0">
              <CardTitle className="text-sm text-muted-foreground">
                {i === 1
                  ? "Mood Trend (Last 7 days)"
                  : i === 2
                  ? "Pain Level Over Time"
                  : i === 3
                  ? "Pain Level Over Time"
                  : "Pain Level Over Time"}
              </CardTitle>
            </CardHeader>
            <CardContent className="h-full flex items-center justify-center">
              <Badge variant="outline">Placeholder</Badge>
            </CardContent>
          </Card>
        ))}
      </section>

      <Separator />
    </div>
  );
}
