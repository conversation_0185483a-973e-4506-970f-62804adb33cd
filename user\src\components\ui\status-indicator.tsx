'use client'

import { useEffect, useState } from 'react'

interface StatusIndicatorProps {
  isOnline: boolean
  isMobile: boolean
}

export function StatusIndicator({ isOnline, isMobile }: StatusIndicatorProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Don't render anything until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className="flex items-center gap-2">
        <span className="text-xs px-2 py-0.5 rounded bg-gray-200">Loading...</span>
        <span className="text-xs px-2 py-0.5 rounded bg-gray-200">Loading...</span>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-xs px-2 py-0.5 rounded bg-gray-200">
        {isMobile ? 'Mobile' : 'Desktop'}
      </span>
      <span 
        className={`text-xs px-2 py-0.5 rounded ${
          isOnline ? 'bg-green-200' : 'bg-red-200'
        }`}
      >
        {isOnline ? 'Online' : 'Offline'}
      </span>
    </div>
  )
}