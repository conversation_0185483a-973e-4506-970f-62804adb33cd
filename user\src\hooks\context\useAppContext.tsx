'use client'

import React, { createContext, useContext, useEffect, ReactNode, useRef } from 'react'
import { useStore } from 'zustand'
import { AppState, initialAppState } from './useAppState'
import { AppStore, createAppStore } from './useAppStore'

// Context to provide the store instance
const AppStoreContext = createContext<ReturnType<typeof createAppStore> | null>(null)

// Provider props
interface AppProviderProps {
  children: ReactNode
  initialState?: Partial<AppState>
}

// Provider component that combines Zustand with Context API
export function AppProvider({ children, initialState }: AppProviderProps) {
  // Create store instance only once per provider
  const storeRef = useRef<ReturnType<typeof createAppStore>>()
  
  if (!storeRef.current) {
    const mergedInitialState = { ...initialAppState, ...initialState }
    storeRef.current = createAppStore(mergedInitialState)
  }

  const store = storeRef.current

  // Initialize app state on mount (SSR-safe) and attach listeners
  useEffect(() => {
    if (typeof window === 'undefined') return

    let resizeRaf: number | null = null

    const checkMobile = () => {
      const nextIsMobile = window.innerWidth < 768
      const currentIsMobile = store.getState().isMobile
      if (nextIsMobile !== currentIsMobile) {
        store.getState().setMobileStatus(nextIsMobile)
      }
    }

    const onResize = () => {
      if (resizeRaf !== null) cancelAnimationFrame(resizeRaf)
      resizeRaf = requestAnimationFrame(checkMobile)
    }

    // Online/offline handlers
    const handleOnline = () => store.getState().setOnlineStatus(true)
    const handleOffline = () => store.getState().setOnlineStatus(false)

    // Initial setup
    checkMobile()
    
    if (typeof navigator !== 'undefined' && typeof navigator.onLine === 'boolean') {
      store.getState().setOnlineStatus(navigator.onLine)
    }

    window.addEventListener('resize', onResize, { passive: true })
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Load theme and settings from localStorage
    try {
      const savedTheme = window.localStorage?.getItem('theme') as AppState['ui']['theme']
      if (savedTheme && savedTheme !== store.getState().ui.theme) {
        store.getState().setTheme(savedTheme)
      }
    } catch (e) {
      console.warn('Theme localStorage read failed:', e)
    }

    try {
      const savedSettings = window.localStorage?.getItem('app-settings')
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings)
        const currentSettings = store.getState().settings
        const hasDiff = Object.keys(parsed).some(
          (k) => (parsed as any)[k] !== (currentSettings as any)[k]
        )
        if (hasDiff) {
          store.getState().updateSettings(parsed)
        }
      }
    } catch (error) {
      console.warn('Failed to load settings from localStorage:', error)
    }

    return () => {
      if (resizeRaf !== null) cancelAnimationFrame(resizeRaf)
      window.removeEventListener('resize', onResize)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [store])

  // Persist theme changes to localStorage
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    const unsubscribe = store.subscribe(
      (state) => state.ui.theme,
      (theme) => {
        try {
          const current = window.localStorage.getItem('theme')
          if (current !== theme) {
            window.localStorage.setItem('theme', theme)
          }
        } catch {}
      }
    )

    return unsubscribe
  }, [store])

  // Persist settings changes to localStorage
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    const unsubscribe = store.subscribe(
      (state) => state.settings,
      (settings) => {
        try {
          const existing = window.localStorage.getItem('app-settings')
          const next = JSON.stringify(settings)
          if (existing !== next) {
            window.localStorage.setItem('app-settings', next)
          }
        } catch {}
      }
    )

    return unsubscribe
  }, [store])

  // Auto-remove notifications after duration
  useEffect(() => {
    const unsubscribe = store.subscribe(
      (state) => state.ui.notifications,
      (notifications) => {
        notifications.forEach((notification) => {
          if (notification.duration) {
            setTimeout(() => {
              // Check if notification still exists before removing
              const currentNotifications = store.getState().ui.notifications
              if (currentNotifications.find(n => n.id === notification.id)) {
                store.getState().removeNotification(notification.id)
              }
            }, notification.duration)
          }
        })
      }
    )

    return unsubscribe
  }, [store])

  return (
    <AppStoreContext.Provider value={store}>
      {children}
    </AppStoreContext.Provider>
  )
}

// Hook to get the store instance from context
function useAppStoreContext() {
  const store = useContext(AppStoreContext)
  if (!store) {
    throw new Error('useAppStoreContext must be used within an AppProvider')
  }
  return store
}

// Main hook to use the app store (with context support)
export function useAppContext() {
  const store = useAppStoreContext()
  return useStore(store)
}

// Convenience hooks for specific state slices with optimized selectors
export function useAuth() {
  const store = useAppStoreContext()
  
  // Subscribe only to auth-related state
  const authState = useStore(store, (state) => state.auth)
  const setUser = useStore(store, (state) => state.setUser)
  const setAuthLoading = useStore(store, (state) => state.setAuthLoading)
  const setAuthError = useStore(store, (state) => state.setAuthError)
  const setToken = useStore(store, (state) => state.setToken)
  const logout = useStore(store, (state) => state.logout)

  return {
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    error: authState.error,
    token: authState.token,
    setUser,
    setAuthLoading,
    setAuthError,
    setToken,
    logout,
  }
}

export function useUI() {
  const store = useAppStoreContext()
  
  const uiState = useStore(store, (state) => state.ui)
  const toggleSidebar = useStore(store, (state) => state.toggleSidebar)
  const setSidebarOpen = useStore(store, (state) => state.setSidebarOpen)
  const setTheme = useStore(store, (state) => state.setTheme)
  const addNotification = useStore(store, (state) => state.addNotification)
  const removeNotification = useStore(store, (state) => state.removeNotification)
  const setLoadingState = useStore(store, (state) => state.setLoadingState)
  const setModalState = useStore(store, (state) => state.setModalState)

  return {
    sidebarOpen: uiState.sidebarOpen,
    theme: uiState.theme,
    notifications: uiState.notifications,
    loadingStates: uiState.loadingStates,
    modals: uiState.modals,
    toggleSidebar,
    setSidebarOpen,
    setTheme,
    addNotification,
    removeNotification,
    setLoadingState,
    setModalState,
  }
}

export function useSettings() {
  const store = useAppStoreContext()
  
  const settings = useStore(store, (state) => state.settings)
  const updateSettings = useStore(store, (state) => state.updateSettings)

  return {
    settings,
    updateSettings,
  }
}

export function useNavigation() {
  const store = useAppStoreContext()
  
  const navigationState = useStore(store, (state) => state.navigation)
  const setCurrentRoute = useStore(store, (state) => state.setCurrentRoute)
  const setBreadcrumbs = useStore(store, (state) => state.setBreadcrumbs)

  return {
    currentRoute: navigationState.currentRoute,
    previousRoute: navigationState.previousRoute,
    breadcrumbs: navigationState.breadcrumbs,
    setCurrentRoute,
    setBreadcrumbs,
  }
}

export function useForms() {
  const store = useAppStoreContext()
  
  const forms = useStore(store, (state) => state.forms)
  const setFormState = useStore(store, (state) => state.setFormState)
  const resetForm = useStore(store, (state) => state.resetForm)

  return {
    forms,
    setFormState,
    resetForm,
  }
}

export function useSystem() {
  const store = useAppStoreContext()
  
  const isOnline = useStore(store, (state) => state.isOnline)
  const isMobile = useStore(store, (state) => state.isMobile)
  const setOnlineStatus = useStore(store, (state) => state.setOnlineStatus)
  const setMobileStatus = useStore(store, (state) => state.setMobileStatus)

  return {
    isOnline,
    isMobile,
    setOnlineStatus,
    setMobileStatus,
  }
}

// Advanced hooks for specific use cases

// Hook for subscribing to specific loading states
export function useLoadingState(key: string) {
  const store = useAppStoreContext()
  return useStore(store, (state) => state.ui.loadingStates[key] || false)
}

// Hook for subscribing to specific modal states
export function useModalState(key: string) {
  const store = useAppStoreContext()
  const isOpen = useStore(store, (state) => state.ui.modals[key] || false)
  const setModalState = useStore(store, (state) => state.setModalState)
  
  return {
    isOpen,
    open: () => setModalState(key, true),
    close: () => setModalState(key, false),
    toggle: () => setModalState(key, !isOpen),
  }
}

// Hook for subscribing to specific form states
export function useFormState(formId: string) {
  const store = useAppStoreContext()
  const formState = useStore(store, (state) => state.forms[formId])
  const setFormState = useStore(store, (state) => state.setFormState)
  const resetForm = useStore(store, (state) => state.resetForm)
  
  return {
    formState,
    setFormState: (state: AppState['forms'][string]) => setFormState(formId, state),
    resetForm: () => resetForm(formId),
  }
}