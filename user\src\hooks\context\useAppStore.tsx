'use client'

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { AppState, initialAppState, Notification } from './useAppState'

// Store interface combining state and actions
export interface AppStore extends AppState {
  // Auth actions
  setUser: (user: AppState['auth']['user']) => void
  setAuthLoading: (loading: boolean) => void
  setAuthError: (error: string | null) => void
  setToken: (token: string | null) => void
  logout: () => void
  
  // UI actions
  toggleSidebar: () => void
  setSidebarOpen: (open: boolean) => void
  setTheme: (theme: AppState['ui']['theme']) => void
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void
  removeNotification: (id: string) => void
  setLoadingState: (key: string, loading: boolean) => void
  setModalState: (key: string, open: boolean) => void
  
  // Settings actions
  updateSettings: (settings: Partial<AppState['settings']>) => void
  
  // Navigation actions
  setCurrentRoute: (route: string) => void
  setBreadcrumbs: (breadcrumbs: AppState['navigation']['breadcrumbs']) => void
  
  // Form actions
  setFormState: (formId: string, formState: AppState['forms'][string]) => void
  resetForm: (formId: string) => void
  
  // System actions
  setOnlineStatus: (online: boolean) => void
  setMobileStatus: (mobile: boolean) => void
  
  // Internal actions for initialization
  _initializeFromState: (state: Partial<AppState>) => void
  _reset: () => void
}

// Create the Zustand store with subscribeWithSelector middleware for fine-grained subscriptions
export const createAppStore = (initialState: AppState = initialAppState) =>
  create<AppStore>()(
    subscribeWithSelector((set, get) => ({
      // Initial state
      ...initialState,
      
      // Auth actions
      setUser: (user) =>
        set((state) => ({
          auth: {
            ...state.auth,
            user,
            isAuthenticated: !!user,
          },
        })),
      
      setAuthLoading: (loading) =>
        set((state) => ({
          auth: {
            ...state.auth,
            isLoading: loading,
          },
        })),
      
      setAuthError: (error) =>
        set((state) => ({
          auth: {
            ...state.auth,
            error,
          },
        })),
      
      setToken: (token) =>
        set((state) => ({
          auth: {
            ...state.auth,
            token,
          },
        })),
      
      logout: () =>
        set((state) => ({
          auth: {
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
            token: null,
          },
        })),
      
      // UI actions
      toggleSidebar: () =>
        set((state) => ({
          ui: {
            ...state.ui,
            sidebarOpen: !state.ui.sidebarOpen,
          },
        })),
      
      setSidebarOpen: (open) =>
        set((state) => ({
          ui: {
            ...state.ui,
            sidebarOpen: open,
          },
        })),
      
      setTheme: (theme) =>
        set((state) => ({
          ui: {
            ...state.ui,
            theme,
          },
        })),
      
      addNotification: (notification) =>
        set((state) => {
          const newNotification: Notification = {
            ...notification,
            id: Date.now().toString(),
            createdAt: new Date(),
          }
          return {
            ui: {
              ...state.ui,
              notifications: [...state.ui.notifications, newNotification],
            },
          }
        }),
      
      removeNotification: (id) =>
        set((state) => ({
          ui: {
            ...state.ui,
            notifications: state.ui.notifications.filter((n) => n.id !== id),
          },
        })),
      
      setLoadingState: (key, loading) =>
        set((state) => ({
          ui: {
            ...state.ui,
            loadingStates: {
              ...state.ui.loadingStates,
              [key]: loading,
            },
          },
        })),
      
      setModalState: (key, open) =>
        set((state) => ({
          ui: {
            ...state.ui,
            modals: {
              ...state.ui.modals,
              [key]: open,
            },
          },
        })),
      
      // Settings actions
      updateSettings: (settings) =>
        set((state) => ({
          settings: {
            ...state.settings,
            ...settings,
          },
        })),
      
      // Navigation actions
      setCurrentRoute: (route) =>
        set((state) => ({
          navigation: {
            ...state.navigation,
            previousRoute: state.navigation.currentRoute,
            currentRoute: route,
          },
        })),
      
      setBreadcrumbs: (breadcrumbs) =>
        set((state) => ({
          navigation: {
            ...state.navigation,
            breadcrumbs,
          },
        })),
      
      // Form actions
      setFormState: (formId, formState) =>
        set((state) => ({
          forms: {
            ...state.forms,
            [formId]: formState,
          },
        })),
      
      resetForm: (formId) =>
        set((state) => {
          const { [formId]: removed, ...remainingForms } = state.forms
          return {
            forms: remainingForms,
          }
        }),
      
      // System actions
      setOnlineStatus: (online) =>
        set(() => ({
          isOnline: online,
        })),
      
      setMobileStatus: (mobile) =>
        set(() => ({
          isMobile: mobile,
        })),
      
      // Internal actions
      _initializeFromState: (state) =>
        set((currentState) => ({
          ...currentState,
          ...state,
        })),
      
      _reset: () => set(initialState),
    }))
  )

// Default store instance (can be overridden by context)
export const useAppStore = createAppStore()